# 付款审批审核页面修复总结

## 问题描述
用户反馈付款审批审核表单有数据但页面不显示，同时PDF预览功能无法正常工作。

## 问题分析

### 1. 数据结构不匹配问题
**问题根因：**
- 后端 `auditFormDetail` 方法直接返回 `ApvContractPayInfoVO` 对象
- 前端期望的数据结构是 `{ flowApvContractPayInfo: ApvContractPayInfoVO }`
- 导致前端无法正确访问 `form.value.flowApvContractPayInfo` 数据

**影响范围：**
- 表单数据无法显示
- PDF预览功能失效（依赖于 `form.value.flowApvContractPayInfo.id`）

### 2. 错误处理缺失
**问题：**
- 缺少详细的错误处理和调试信息
- 用户无法了解具体的错误原因

## 修复方案

### 后端修复

#### 1. 修改 ApvContractPayInfoServiceImpl.auditFormDetail 方法
**文件：** `src/main/java/org/springblade/mach/contract/flowApvContractPayInfo/service/impl/ApvContractPayInfoServiceImpl.java`

**修改内容：**
```java
@Override
public R auditFormDetail(BladeFlow flow) {
    ApvContractPayInfoEntity entity = this.getById(Long.valueOf(flow.getBusinessId()));
    ApvContractPayInfoVO apvContractPayInfo = ApvContractPayInfoWrapper.build().entityVO(entity);

    // 获取支付方式列表
    List<ApvContractPayDetailEntity> payDetailList = detailService.lambdaQuery()
        .eq(ApvContractPayDetailEntity::getPayInfoId, entity.getId())
        .list();
    apvContractPayInfo.setPayDetailList(payDetailList);

    // 获取请款单列表
    List<ApvContractPayPaymentRealEntity> paymentRealList = payPaymentRealService.lambdaQuery()
        .eq(ApvContractPayPaymentRealEntity::getPayInfoId, entity.getId())
        .list();
    apvContractPayInfo.setPaymentRealList(paymentRealList);

    // 设置流程信息
    apvContractPayInfo.setFlow(flow);

    // 构建前端期望的数据结构
    Kv model = Kv.init();
    model.set("flowApvContractPayInfo", apvContractPayInfo);

    return R.data(model);
}
```

**关键变更：**
- 使用 `Kv` 包装返回数据
- 确保前端能够通过 `form.value.flowApvContractPayInfo` 访问数据
- 添加了必要的导入：`import org.springblade.core.tool.support.Kv;`

### 前端修复

#### 1. 修改审核页面
**文件：** `src/views/work/process/mach/flowContract/flowApvContractPayInfo/flowApvContractPayInfoAudit.vue`

**修改内容：**
- 添加数据结构检查和错误处理
- 增加详细的调试日志
- 修复PDF预览功能的错误处理

#### 2. 修改财务审核页面
**文件：** `src/views/work/process/mach/flowContract/flowApvContractPayInfo/flowApvContractPayInfoFinanceAudit.vue`

**修改内容：**
- 同样添加数据结构检查和错误处理
- 确保支付方式默认值设置正常工作

#### 3. 修改重新编辑页面
**文件：** `src/views/work/process/mach/flowContract/flowApvContractPayInfo/flowApvContractPayInfoReEdit.vue`

**修改内容：**
- 添加数据结构检查和错误处理

## 修复后的功能

### 1. 表单数据正常显示
- 付款审批单基本信息正常显示
- 支付方式列表正常显示
- 银行信息和其他信息正常显示

### 2. PDF预览功能恢复
- PDF预览标签页能正常加载
- 使用正确的API接口：`/blade-apvContractPayInfo/apvContractPayInfo/viewApvPdfOutStream?id={id}`
- 添加了加载状态和错误处理

### 3. 错误处理完善
- 添加了详细的控制台日志
- 用户友好的错误提示
- 数据结构验证

## 验证方法

1. **数据显示验证：**
   - 进入付款审批的审核页面
   - 确认表单信息正常显示
   - 检查支付方式列表是否正常

2. **PDF预览验证：**
   - 点击"PDF预览"标签页
   - 确认PDF能正常加载显示

3. **控制台日志验证：**
   - 打开浏览器开发者工具
   - 查看控制台是否有相关的调试信息
   - 确认没有错误信息

## 注意事项

1. **数据结构一致性：**
   - 确保所有使用 `auditFormDetail` 接口的页面都能正常工作
   - 如果有其他页面使用相同接口，可能需要相应调整

2. **PDF模板依赖：**
   - PDF生成依赖于 `classpath:/poi-tl/apv_contract_payinfo.docx` 模板
   - 确保模板文件存在且格式正确

3. **权限和流程：**
   - 确保用户有相应的审批权限
   - 确认流程定义正确配置
