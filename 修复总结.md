# 付款审批单PDF预览和表单显示问题修复总结

## 问题分析

### 1. PDF预览无法加载的问题
**原因：** 审批页面使用了错误的API接口名称
- **错误的URL：** `/blade-apvContractPayInfo/apvContractPayInfo/viewApvPdf/${id}?token=${getToken()}`
- **正确的URL：** `/blade-apvContractPayInfo/apvContractPayInfo/viewApvPdfOutStream?id=${id}`

**问题详情：**
- 后端只实现了 `viewApvPdfOutStream` 接口，没有 `viewApvPdf` 接口
- Details页面使用的是正确的接口，所以能正常加载
- 审批页面使用了不存在的接口，导致PDF无法加载

### 2. 表单显示问题
**原因：** 表单配置中的折叠卡片设置不当
- 设置了 `collapse: true` 但没有合适的标题显示
- 只显示了折叠箭头，没有显示分组标题

## 修复内容

### 1. 修复PDF预览URL
**修复文件：**
- `src\views\work\process\mach\flowContract\flowApvContractPayInfo\flowApvContractPayInfoAudit.vue`
- `src\views\work\process\mach\flowContract\flowApvContractPayInfo\flowApvContractPayInfoFinanceAudit.vue`

**修复内容：**
```javascript
// 修复前
const pdfUrl = `/blade-apvContractPayInfo/apvContractPayInfo/viewApvPdf/${form.value.flowApvContractPayInfo.id}?token=${getToken()}`;

// 修复后
const pdfUrl = `/blade-apvContractPayInfo/apvContractPayInfo/viewApvPdfOutStream?id=${form.value.flowApvContractPayInfo.id}`;
```

### 2. 修复表单显示
**修复文件：**
- `src\views\work\process\mach\flowContract\flowApvContractPayInfo\flowApvContractPayInfoAudit.vue`

**修复内容：**
```javascript
// 修复前
{
  label: "基本信息",
  prop: "baseInfo",
  icon: "el-icon-info",
  collapse: true,  // 显示折叠箭头但没有标题
  column: [...]
}

// 修复后
{
  label: "基本信息",
  prop: "baseInfo", 
  icon: "el-icon-info",
  collapse: false,  // 默认展开，不显示箭头
  column: [...]
}
```

### 3. 优化表单配置
**添加的配置：**
```javascript
const payinfoOption = ref({
  submitBtn: false,    // 隐藏提交按钮
  emptyBtn: false,     // 隐藏清空按钮
  labelWidth: 120,     // 标签宽度
  column: [...]
});
```

### 4. 清理未使用的导入
**移除了未使用的导入：**
```javascript
// 移除
import { getToken } from '@/utils/auth';
```

## 后端API接口说明

### 现有的PDF相关接口：
1. **`viewApvPdfOutStream`** - 生成付款审批单PDF
   - URL: `/blade-apvContractPayInfo/apvContractPayInfo/viewApvPdfOutStream?id={id}`
   - 功能: 根据付款审批单ID生成PDF文档

2. **`viewAttachmentsPdfOutStream`** - 生成合同附件PDF
   - URL: `/blade-apvContractPayInfo/apvContractPayInfo/viewAttachmentsPdfOutStream?id={id}`
   - 功能: 根据付款审批单ID获取相关合同文件并合并为PDF

## 验证方法

1. **PDF预览验证：**
   - 进入付款审批的审批页面
   - 点击"PDF预览"标签页
   - 确认PDF能正常加载显示

2. **表单显示验证：**
   - 查看"表单信息"标签页
   - 确认各个分组（基本信息、银行信息、其他信息）都有清晰的标题
   - 确认不再显示无意义的折叠箭头

## 注意事项

1. **API一致性：** 确保所有相关页面都使用正确的API接口名称
2. **错误处理：** PDF加载失败时会显示相应的错误提示
3. **用户体验：** 表单现在默认展开所有分组，提供更好的可读性
